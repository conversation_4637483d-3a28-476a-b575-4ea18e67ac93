/* Mobile Homepage Styles */
.homepage-mobile {
  background-color: #FBFBF9;
  min-height: 100vh;
  width: 100%;
  overflow-x: hidden;
  font-family: "Source Sans Pro", sans-serif;
}

/* Hero Section */
.homepage-mobile__hero {
  padding: 80px 20px 40px 20px;
  background-color: #FBFBF9;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.homepage-mobile__hero-content {
  display: flex;
  flex-direction: column;
  gap: 40px;
  max-width: 100%;
}

.homepage-mobile__hero-title {
  font-family: "Baskerville Old Face", "Times New Roman", serif;
  font-size: clamp(36px, 8vw, 48px);
  line-height: 1.1;
  color: #b99c6d;
  font-weight: normal;
  margin: 0;
  text-align: left;
  letter-spacing: -0.02em;
}

.homepage-mobile__features-list {
  display: flex;
  flex-direction: column;
  gap: 25px;
  margin: 40px 0;
}

.homepage-mobile__feature-item {
  display: flex;
  align-items: flex-start;
  gap: 18px;
  padding: 0;
}

.homepage-mobile__feature-icon {
  width: 48px;
  height: 48px;
  flex-shrink: 0;
  object-fit: contain;
}

.homepage-mobile__feature-content {
  flex: 1;
  padding-top: 2px;
}

.homepage-mobile__feature-title {
  font-family: "Battambang", sans-serif;
  font-size: 20px;
  font-weight: 600;
  color: #966f33;
  margin: 0 0 8px 0;
  letter-spacing: -0.05em;
}

.homepage-mobile__feature-description {
  font-family: "Source Sans Pro", sans-serif;
  font-size: 15px;
  color: #966f33;
  margin: 0;
  line-height: 1.3;
  letter-spacing: -0.05em;
}

.homepage-mobile__hero-bottom {
  display: flex;
  flex-direction: column;
  gap: 30px;
  margin-top: 40px;
  padding-top: 20px;
}

.homepage-mobile__hero-subtitle {
  font-family: "Baskerville Old Face", "Times New Roman", serif;
  font-size: clamp(20px, 5vw, 26px);
  color: #b99c6d;
  margin: 0;
  line-height: 1.1;
  letter-spacing: -0.05em;
}

/* Button will use existing Button component styles */

/* Content Creation Section */
.homepage-mobile__content-section {
  background-color: #FFF8E4;
  padding: 60px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 40px;
  min-height: 100vh;
  justify-content: center;
}

.homepage-mobile__feature-image {
  background-image: url("/feature1.png");
  background-size: cover;
  background-position: center;
  box-shadow: 0px 4px 47.7px 0px rgba(0, 0, 0, 0.25);
  width: 100%;
  max-width: 400px;
  height: 300px;
  flex-shrink: 0;
}

.homepage-mobile__content-text {
  text-align: center;
  max-width: 100%;
}

.homepage-mobile__section-title {
  font-family: "Baskerville Old Face", "Times New Roman", serif;
  font-size: 32px;
  color: #3c1f13;
  margin: 0 0 30px 0;
  line-height: 1.1;
  letter-spacing: -0.05em;
}

.homepage-mobile__content-features {
  display: flex;
  flex-direction: column;
  gap: 20px;
  text-align: left;
}

.homepage-mobile__content-feature {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.homepage-mobile__content-icon {
  font-size: 18px;
  margin-top: 2px;
}

.homepage-mobile__content-description {
  font-family: "Source Sans Pro", sans-serif;
  font-size: 16px;
  color: #3c1f13;
  margin: 0;
  line-height: 1.3;
  letter-spacing: -0.05em;
}

/* Testimonials Section */
.homepage-mobile__testimonials {
  background-color: #FBFBF9;
  padding: 60px 20px;
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.homepage-mobile__testimonials-content {
  text-align: center;
  width: 100%;
}

.homepage-mobile__testimonials-title {
  font-family: "Baskerville Old Face", "Times New Roman", serif;
  font-size: clamp(28px, 6vw, 36px);
  color: #5d4037;
  margin: 0 0 40px 0;
  letter-spacing: 2px;
  line-height: 1.2;
}

.homepage-mobile__testimonial-quote {
  margin: 40px 0;
  padding: 0 10px;
}

.homepage-mobile__quote-mark {
  font-size: 80px;
  color: #966f33;
  font-family: "Baskerville Old Face", serif;
  display: block;
  margin-bottom: 25px;
  line-height: 1;
}

.homepage-mobile__testimonial-text {
  font-family: "Josefin Sans", sans-serif;
  font-style: italic;
  font-size: clamp(16px, 4vw, 20px);
  color: black;
  line-height: 1.6;
  margin: 0 0 40px 0;
  max-width: 90%;
  margin-left: auto;
  margin-right: auto;
  letter-spacing: -0.05em;
}

.homepage-mobile__testimonial-rating {
  margin: 30px 0;
}

.homepage-mobile__star {
  font-size: 28px;
  margin: 0 3px;
  color: #966f33;
}

.homepage-mobile__testimonial-author {
  font-family: "Source Sans Pro", sans-serif;
  font-size: 15px;
  color: black;
  margin: 30px 0 0 0;
  font-weight: 500;
}

/* Team Section */
.homepage-mobile__team {
  background-color: #FFF8E4;
  padding: 60px 20px;
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.homepage-mobile__team-content {
  text-align: center;
  width: 100%;
}

.homepage-mobile__team-title {
  font-family: "Baskerville Old Face", "Times New Roman", serif;
  font-size: clamp(28px, 6vw, 36px);
  color: #966f33;
  margin: 0 0 40px 0;
  letter-spacing: -0.025em;
  line-height: 1.1;
}

.homepage-mobile__team-member {
  margin: 40px 0;
}

.homepage-mobile__team-image {
  width: 220px;
  height: 280px;
  object-fit: cover;
  border-radius: 0;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.homepage-mobile__team-quote {
  margin: 40px 0;
  padding: 0 10px;
}

.homepage-mobile__team-text {
  font-family: "Josefin Sans", sans-serif;
  font-style: italic;
  font-size: clamp(15px, 3.5vw, 18px);
  color: black;
  line-height: 1.6;
  margin: 0;
  max-width: 90%;
  margin-left: auto;
  margin-right: auto;
  letter-spacing: -0.05em;
}

.homepage-mobile__team-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 30px 0;
  padding: 0 10px;
}

.homepage-mobile__team-name {
  font-family: "Source Sans Pro", sans-serif;
  font-size: 15px;
  color: black;
  margin: 0;
  flex: 1;
  text-align: left;
  font-weight: 500;
}

.homepage-mobile__team-arrow {
  background: none;
  border: none;
  font-size: 24px;
  color: #3e3e3e;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.homepage-mobile__team-arrow:hover {
  background-color: rgba(139, 69, 19, 0.1);
  color: #8B4513;
}

.homepage-mobile__team-rating {
  margin: 30px 0;
}

/* Responsive adjustments for very small screens */
@media (max-width: 480px) {
  .homepage-mobile__hero-title {
    font-size: 32px;
  }

  .homepage-mobile__section-title {
    font-size: 24px;
  }

  .homepage-mobile__testimonials-title,
  .homepage-mobile__team-title {
    font-size: 24px;
  }

  .homepage-mobile__hero {
    padding: 60px 15px 30px 15px;
  }

  .homepage-mobile__content-section,
  .homepage-mobile__testimonials,
  .homepage-mobile__team {
    padding: 40px 15px;
  }

  .homepage-mobile__feature-icon {
    width: 44px;
    height: 44px;
    font-size: 20px;
  }

  .homepage-mobile__icon-item {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }

  .homepage-mobile__team-image {
    width: 180px;
    height: 240px;
  }
}

/* Ensure smooth scrolling */
.homepage-mobile {
  scroll-behavior: smooth;
}

/* Touch-friendly interactions */
@media (hover: none) and (pointer: coarse) {
  .homepage-mobile__team-arrow:hover {
    background-color: transparent;
    color: #3e3e3e;
  }

  .homepage-mobile button:hover {
    background-color: #8B4513;
    transform: none;
  }
}

/* Accessibility improvements */
.homepage-mobile button:focus,
.homepage-mobile__team-arrow:focus {
  outline: 2px solid #8B4513;
  outline-offset: 2px;
}

/* Ensure proper spacing between sections */
.homepage-mobile section + section {
  margin-top: 0;
}

/* Fix any potential overflow issues */
.homepage-mobile * {
  box-sizing: border-box;
}
