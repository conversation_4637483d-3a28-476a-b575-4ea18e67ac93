import Button from "./Button";
import "./HomepageMobile.css";
import Navbar from "./Navbar";
import WhyUs from "./WhyUs";
import WaitlistPopup from "./WaitlistPopup";
import Footer from "./Footer";
import { useState } from "react";

interface HomepageMobileProps {
  onJoinWaitlist: () => void;
}

const HomepageMobile = ({ onJoinWaitlist }: HomepageMobileProps) => {
  const [isWaitlistPopupOpen, setIsWaitlistPopupOpen] = useState(false);

  const handleJoinWaitlist = () => {
    setIsWaitlistPopupOpen(true);
  };

  const handleCloseWaitlistPopup = () => {
    setIsWaitlistPopupOpen(false);
  };

  // Use the same features array as desktop version
  const features = [
    {
      icon: "https://api.builder.io/api/v1/image/assets/TEMP/aa858a2f8ed0a134930f72e7fef50e364573ebfc?width=118",
      title: "Features",
      description: "Benefit of joining waiting list",
      iconClass: "homepage-mobile__feature-icon",
    },
    {
      icon: "https://api.builder.io/api/v1/image/assets/TEMP/9badbe856312fc48be0805e349db264b09d2846c?width=118",
      title: "Why You should Join us",
      description: "Urgency of joining waiting list",
      iconClass: "homepage-mobile__feature-icon",
    },
    {
      icon: "https://api.builder.io/api/v1/image/assets/TEMP/189e56502619bfcd3df1b9c9dfa220747ca30a92?width=118",
      title: "Testimonials",
      description: "Benefit of joining waiting list",
      iconClass: "homepage-mobile__feature-icon",
    },
  ];

  const contentFeatures = [
    {
      icon: "✨",
      description: "AI-Powered Writing Tools with legal-specific training and compliance checks",
    },
    {
      icon: "🔧",
      description: "AI-Powered Writing Tools with legal-specific training and compliance checks",
    },
  ];

  return (
    <div className="homepage-mobile">
      <Navbar onJoinWaitlist={handleJoinWaitlist} />

      {/* Hero Section */}
      <section className="homepage-mobile__hero">
        <div className="homepage-mobile__hero-content">
          <h1 className="homepage-mobile__hero-title">
            Be a legal entrepreneur
          </h1>

          <div className="homepage-mobile__features-list">
            {features.map((feature, index) => (
              <div key={index} className="homepage-mobile__feature-item">
                <img
                  src={feature.icon}
                  alt=""
                  className={feature.iconClass}
                />
                <div className="homepage-mobile__feature-content">
                  <h3 className="homepage-mobile__feature-title">
                    {feature.title}
                  </h3>
                  <p className="homepage-mobile__feature-description">
                    {feature.description}
                  </p>
                </div>
              </div>
            ))}
          </div>

          <div className="homepage-mobile__hero-bottom">
            <p className="homepage-mobile__hero-subtitle">
              Creator platform for modern — legal professionals
            </p>

            <Button size="large" onClick={handleJoinWaitlist}>
              Join Waitlist
            </Button>
          </div>
        </div>
      </section>

      {/* Content Creation Section */}
      <section className="homepage-mobile__content-section">
        <div className="homepage-mobile__feature-image"></div>

        <div className="homepage-mobile__content-text">
          <h2 className="homepage-mobile__section-title">
            Content Creation & Research Engine
          </h2>

          <div className="homepage-mobile__content-features">
            {contentFeatures.map((feature, index) => (
              <div key={index} className="homepage-mobile__content-feature">
                <span className="homepage-mobile__content-icon">{feature.icon}</span>
                <p className="homepage-mobile__content-description">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Why Us Section */}
      <WhyUs />

      {/* Testimonials Section */}
      <section className="homepage-mobile__testimonials">
        <div className="homepage-mobile__testimonials-content">
          <h2 className="homepage-mobile__testimonials-title">
            Our Customer's Opinions
          </h2>

          <div className="homepage-mobile__testimonial-quote">
            <span className="homepage-mobile__quote-mark">"</span>
            <p className="homepage-mobile__testimonial-text">
              Extensive substantive knowledge, extensive experience, reliability,
              commitment and availability of a team of specialists ensure the
              highest level of service.
            </p>
          </div>


          <p className="homepage-mobile__testimonial-author">
            Name of the person, Their profession
          </p>
        </div>
      </section>

      {/* Team Section */}
      <section className="homepage-mobile__team">
        <div className="homepage-mobile__team-content">
          <h2 className="homepage-mobile__team-title">
            What our team has to say!
          </h2>

          <div className="homepage-mobile__team-member">
            <img
              src="/team-member.jpg"
              alt="Team Member"
              className="homepage-mobile__team-image"
            />
          </div>

          <div className="homepage-mobile__team-quote">
            <p className="homepage-mobile__team-text">
              "Amplify the digital footprint of contributing legal experts.
              Detailed author profiles and multi-channel promotion boost
              their professional authority."
            </p>
          </div>

          <div className="homepage-mobile__team-info">
            <p className="homepage-mobile__team-name">
              This is Sahil Saurav, Meet the Whole team
            </p>
            <button className="homepage-mobile__team-arrow">→</button>
          </div>

          
        </div>
      </section>

      <Footer />

      {isWaitlistPopupOpen && (
        <WaitlistPopup onClose={handleCloseWaitlistPopup} />
      )}
    </div>
  );
};

export default HomepageMobile;
