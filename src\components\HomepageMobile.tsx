import Button from "./Button";
import "./HomepageMobile.css";
import Navbar from "./Navbar";
import WhyUs from "./WhyUs";
import WaitlistPopup from "./WaitlistPopup";
import Footer from "./Footer";
import { useState } from "react";

interface HomepageMobileProps {
  onJoinWaitlist: () => void;
}

const HomepageMobile = ({ onJoinWaitlist }: HomepageMobileProps) => {
  const [isWaitlistPopupOpen, setIsWaitlistPopupOpen] = useState(false);

  const handleJoinWaitlist = () => {
    setIsWaitlistPopupOpen(true);
    onJoinWaitlist();
  };

  const handleCloseWaitlistPopup = () => {
    setIsWaitlistPopupOpen(false);
  };

  const features = [
    {
      icon: "https://api.builder.io/api/v1/image/assets/TEMP/189e56502619bfcd3df1b9c9dfa220747ca30a92?width=118",
      title: "Content Creation",
      description: "AI-powered writing tools for legal professionals",
      iconClass: "homepage-mobile__feature-icon",
    },
    {
      icon: "https://api.builder.io/api/v1/image/assets/TEMP/189e56502619bfcd3df1b9c9dfa220747ca30a92?width=118",
      title: "Research Engine",
      description: "Legal-specific research and compliance checks",
      iconClass: "homepage-mobile__feature-icon",
    },
    {
      icon: "https://api.builder.io/api/v1/image/assets/TEMP/189e56502619bfcd3df1b9c9dfa220747ca30a92?width=118",
      title: "Monetization",
      description: "Unlock new revenue opportunities",
      iconClass: "homepage-mobile__feature-icon",
    },
    {
      icon: "https://api.builder.io/api/v1/image/assets/TEMP/189e56502619bfcd3df1b9c9dfa220747ca30a92?width=118",
      title: "Community",
      description: "Connect with legal professionals",
      iconClass: "homepage-mobile__feature-icon",
    },
  ];

  return (
    <div className="homepage-mobile">
      <Navbar onJoinWaitlist={handleJoinWaitlist} />
      
      {/* Hero Section */}
      <section className="homepage-mobile__hero">
        <div className="homepage-mobile__hero-content">
          <h1 className="homepage-mobile__hero-title">
            <span className="homepage-mobile__hero-word">Be</span>{" "}
            <span className="homepage-mobile__hero-word">a</span>{" "}
            <span className="homepage-mobile__hero-word">legal</span>
            <br />
            <span className="homepage-mobile__hero-word">entrepreneur</span>
          </h1>
          
          <p className="homepage-mobile__hero-subtitle">
            Creator platform for modern legal professionals
          </p>
          
          <div className="homepage-mobile__hero-description">
            "LawVriksh is the first AI-powered platform that helps
            legal experts build a respected online voice, create high-impact
            content, and unlock new monetization opportunities."
          </div>
          
          <Button size="large" onClick={handleJoinWaitlist}>
            Join Waitlist
          </Button>
        </div>
      </section>

      {/* Features Section */}
      <section className="homepage-mobile__features">
        <div className="homepage-mobile__features-header">
          <h2 className="homepage-mobile__features-title">
            Why Choose LawVriksh?
          </h2>
        </div>
        
        <div className="homepage-mobile__features-grid">
          {features.map((feature, index) => (
            <div key={index} className="homepage-mobile__feature-item">
              <img 
                src={feature.icon} 
                alt="" 
                className={feature.iconClass} 
              />
              <div className="homepage-mobile__feature-content">
                <h3 className="homepage-mobile__feature-title">
                  {feature.title}
                </h3>
                <p className="homepage-mobile__feature-description">
                  {feature.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Content Creation Section */}
      <section className="homepage-mobile__content-section">
        <div className="homepage-mobile__content-image">
          <img 
            src="/feature1.png" 
            alt="Content Creation Engine" 
            className="homepage-mobile__feature-image"
          />
        </div>
        <div className="homepage-mobile__content-text">
          <h2 className="homepage-mobile__section-title">
            Content Creation & Research Engine
          </h2>
          <p className="homepage-mobile__section-description">
            AI-Powered Writing Tools with legal-specific training and
            compliance checks
          </p>
        </div>
      </section>

      {/* Why Us Section */}
      <WhyUs />

      {/* CTA Section */}
      <section className="homepage-mobile__cta">
        <div className="homepage-mobile__cta-content">
          <h2 className="homepage-mobile__cta-title">
            Ready to Transform Your Legal Practice?
          </h2>
          <p className="homepage-mobile__cta-description">
            Join thousands of legal professionals who are already building their digital presence.
          </p>
          <Button size="large" onClick={handleJoinWaitlist}>
            Join Waitlist Now
          </Button>
        </div>
      </section>

      <Footer />

      {isWaitlistPopupOpen && (
        <WaitlistPopup onClose={handleCloseWaitlistPopup} />
      )}
    </div>
  );
};

export default HomepageMobile;
